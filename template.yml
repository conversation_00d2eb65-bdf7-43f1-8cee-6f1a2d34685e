# Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of this
# software and associated documentation files (the "Software"), to deal in the Software
# without restriction, including without limitation the rights to use, copy, modify,
# merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
# INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
# PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
# HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
#
# NOTES
# This CloudFormation template would provision the base infrastructure for the solution
# You don't need to provision any other templates if you're planning to deploy the solution to a single region.

# These lambdas are scheduled to trigger before the mat-db-logicapp-test Azure logic app triggers the HttpTrigger-aws-copy-exports function 
# which then copies the csv files generated in this solution to a central ADLS matdbadlstest
# These Lambdas start sequence running at 15:00 UTC every Saturday
# Azure logic app runs 17:00 UTC every Saturday

Transform: AWS::Serverless-2016-10-31

Globals:
  Function:
    Handler: main.lambda_handler
    Runtime: python3.11
    MemorySize: 2048
    Timeout: 900

Description: Lambda functions to retrieve data from AWS accounts

Resources:
  LambdaPermissionsToCloudMaturityDashboard:
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
      RoleName: !Sub LambdaCloudMaturityDashboard-${AWS::Region}
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/AmazonSSMReadOnlyAccess
        - arn:aws:iam::aws:policy/AWSOrganizationsReadOnlyAccess
      Policies:
        - PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - sts:AssumeRole
                Resource: arn:aws:iam::*:role/OrganizationAccountAccessRole
          PolicyName: AssumeCrossAccountAccessRole
        - PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:PutRetentionPolicy
                  - logs:DescribeLogGroups
                Resource: '*'
          PolicyName: AllowUpdateRetentionPolicy
    Type: AWS::IAM::Role

  CloudMaturityDashboardFunction1:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./src/
      Description: Lambda function to retrieve data from AWS accounts and export to
        CSV in central S3 - us-west-2
      FunctionName: adf-cloud-maturity-dashboard-us-west-2
      FunctionUrlConfig:
        AuthType: NONE
      Role: !GetAtt LambdaPermissionsToCloudMaturityDashboard.Arn
      Runtime: python3.9
      Environment:
        Variables:
          REGION_NAME: us-west-2


  CloudMaturityDashboardFunction2:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./src/
      Description: Lambda function to retrieve data from AWS accounts and export to
        CSV in central S3 - eu-west-1
      FunctionName: adf-cloud-maturity-dashboard-eu-west-1
      FunctionUrlConfig:
        AuthType: NONE
      Role: !GetAtt LambdaPermissionsToCloudMaturityDashboard.Arn
      Runtime: python3.9
      Environment:
        Variables:
          REGION_NAME: eu-west-1

  CloudMaturityDashboardFunction3:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./src/
      Description: Lambda function to retrieve data from AWS accounts and export to
        CSV in central S3 - ap-south-1
      FunctionName: adf-cloud-maturity-dashboard-ap-south-1
      FunctionUrlConfig:
        AuthType: NONE
      Role: !GetAtt LambdaPermissionsToCloudMaturityDashboard.Arn
      Runtime: python3.9
      Environment:
        Variables:
          REGION_NAME: ap-south-1

  CloudMaturityDashboardFunction4:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./src/
      Description: Lambda function to retrieve data from AWS accounts and export to
        CSV in central S3 - ap-northeast-2
      FunctionName: adf-cloud-maturity-dashboard-ap-northeast-2
      FunctionUrlConfig:
        AuthType: NONE
      Role: !GetAtt LambdaPermissionsToCloudMaturityDashboard.Arn
      Runtime: python3.9
      Environment:
        Variables:
          REGION_NAME: ap-northeast-2

  EventRule1:
    Type: AWS::Events::Rule
    Properties:
      Description: Trigger Lambda 1 every week
      ScheduleExpression: cron(0 15 ? * SAT *)
      State: ENABLED
      Targets:
        - Arn: !GetAtt CloudMaturityDashboardFunction1.Arn
          Id: TargetFunctionV1

  PermissionForEventsToInvokeLambda1:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CloudMaturityDashboardFunction1
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt EventRule1.Arn

  EventRule2:
    Type: AWS::Events::Rule
    Properties:
      Description: Trigger Lambda 2 every week
      ScheduleExpression: cron(6 15 ? * SAT *)
      State: ENABLED
      Targets:
        - Arn: !GetAtt CloudMaturityDashboardFunction2.Arn
          Id: TargetFunctionV2

  PermissionForEventsToInvokeLambda2:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CloudMaturityDashboardFunction2
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt EventRule2.Arn

  EventRule3:
    Type: AWS::Events::Rule
    Properties:
      Description: Trigger Lambda 3 every week
      ScheduleExpression: cron(12 15 ? * SAT *)
      State: ENABLED
      Targets:
        - Arn: !GetAtt CloudMaturityDashboardFunction3.Arn
          Id: TargetFunctionV3

  PermissionForEventsToInvokeLambda3:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CloudMaturityDashboardFunction3
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt EventRule3.Arn

  EventRule4:
    Type: AWS::Events::Rule
    Properties:
      Description: Trigger Lambda 4 every week
      ScheduleExpression: cron(18 15 ? * SAT *)
      State: ENABLED
      Targets:
        - Arn: !GetAtt CloudMaturityDashboardFunction4.Arn
          Id: TargetFunctionV4

  PermissionForEventsToInvokeLambda4:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CloudMaturityDashboardFunction4
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt EventRule4.Arn