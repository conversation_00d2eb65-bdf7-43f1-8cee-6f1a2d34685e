# Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of this
# software and associated documentation files (the "Software"), to deal in the Software
# without restriction, including without limitation the rights to use, copy, modify,
# merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
# permit persons to whom the Software is furnished to do so.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
# INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
# PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
# HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
#
# NOTES
# This CloudFormation template would provision the base infrastructure for the solution
# You don't need to provision any other templates if you're planning to deploy the solution to a single region.


Transform: AWS::Serverless-2016-10-31

Globals:
  Function:
    Handler: main.lambda_handler
    Runtime: "python3.9"
    MemorySize: 512
    Timeout: 900

Description:
  Lambda function to retrieve data from AWS accounts and exports to CSV in central S3

Resources:
  LambdaPermissionsToCloudMaturityDashboard:
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          -
            Action: "sts:AssumeRole"
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
      RoleName: !Sub "LambdaCloudMaturityDashboard-${AWS::Region}"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/AmazonSSMReadOnlyAccess
        - arn:aws:iam::aws:policy/AWSOrganizationsReadOnlyAccess
      Policies:
      - PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - "sts:AssumeRole"
              Resource: "arn:aws:iam::*:role/OrganizationAccountAccessRole"
        PolicyName: AssumeCrossAccountAccessRole
      - PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - "logs:PutRetentionPolicy"
                - "logs:DescribeLogGroups"
              Resource: "*"
        PolicyName: AllowUpdateRetentionPolicy
    Type: "AWS::IAM::Role"

  CloudMaturityDashboardFunction:
    Type: "AWS::Serverless::Function"
    Properties:
      CodeUri: ./src/
      Description: Lambda function to retrieve data from AWS accounts and exports to CSV in central S3
      FunctionName: adf-cloud-maturity-dashboard-function
      FunctionUrlConfig:
        AuthType: NONE
      Role: !GetAtt LambdaPermissionsToCloudMaturityDashboard.Arn

  EventRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "Trigger Lambda every week"
      ScheduleExpression: 'rate(7 days)'
      State: "ENABLED"
      Targets:
        -
          Arn:
            Fn::GetAtt:
              - "CloudMaturityDashboardFunction"
              - "Arn"
          Id: "TargetFunctionV1"

  PermissionForEventsToInvokeLambda:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName:
        Ref: "CloudMaturityDashboardFunction"
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn:
        Fn::GetAtt:
          - "EventRule"
          - "Arn"