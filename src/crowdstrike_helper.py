"""Helper module for Crowdstrike API Client"""
import json
import logging
import boto3
import requests
from tenacity import retry, stop_after_attempt, \
wait_exponential, retry_if_exception_type, before_sleep_log

class CrowdStrikeClient:
    '''Helper class for Crowdstrike API Client'''
    def __init__(self, logger: logging.Logger, region: str = "eu-west-1"):
        self.logger = logger
        self.api_url = "https://api.eu-1.crowdstrike.com"
        self.region = region
        self.access_token = self._get_access_token()

    def _get_access_token(self) -> str:
        """Fetch CrowdStrike access token from AWS Secrets Manager."""
        secrets_client = boto3.client("secretsmanager", region_name=self.region)
        secret_name = "crowdstrike_api_credentials"
        secret_response = secrets_client.get_secret_value(SecretId=secret_name)
        secret_dict = json.loads(secret_response["SecretString"])

        payload = {
            "client_id": secret_dict["client_id"],
            "client_secret": secret_dict["client_secret"]
        }

        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        token_url = f"{self.api_url}/oauth2/token"
        response = requests.post(token_url, data=payload, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()['access_token']

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(requests.exceptions.RequestException),
        before_sleep=before_sleep_log(logging.getLogger(__name__), logging.WARNING),
        reraise=True
    )
    def _fetch_batch(self, url: str, headers: dict) -> dict:
        """Perform a single GET request to the CrowdStrike API with retry logic."""
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()

    def get_devices_by_account_id(self, account_id: str) -> list:
        """Fetch all cloud-based devices for a given account ID using cursor-based pagination."""
        fields = "instance_id,hostname,os_version,platform_name,local_ip,agent_version"
        limit = 1000
        next_token = None
        all_devices = []

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        while True:
            filter_query = f"filter=service_provider_account_id:'{account_id}'"
            endpoint_url = "devices/combined/devices/v1"
            base_url = f"{self.api_url}/{endpoint_url}?fields={fields}&{filter_query}&limit={limit}"
            url = f"{base_url}&offset={next_token}" if next_token else base_url

            try:
                data = self._fetch_batch(url, headers)
            except requests.exceptions.HTTPError as http_err:
                self.logger.error("HTTP error: %s - %s", http_err.response.status_code, 
                                  http_err.response.text)
                return all_devices
            except requests.exceptions.RequestException as req_err:
                self.logger.error("Request failed: %s", req_err)
                return all_devices

            devices = data.get("resources", [])
            if not devices and not all_devices:
                self.logger.info("No CrowdStrike devices found for account ID: %s", account_id)
                return []

            all_devices.extend(devices)
            self.logger.info("Fetched %d devices in batch", len(devices))

            next_token = data.get("meta", {}).get("pagination", {}).get("next")
            if not next_token:
                break

        self.logger.info("Total devices fetched: %d", len(all_devices))
        return all_devices

    def enrich_ec2_data(self, ec2_data: dict, account_id: str) -> list:
        """Enrich EC2 data with CrowdStrike device metadata using instance_id."""
        devices = self.get_devices_by_account_id(account_id)
        cs_lookup = {
            device.get("instance_id"): device
            for device in devices
            if device.get("instance_id")
        }

        enriched_data = []

        for platform in ec2_data["platform_details"]:
            for ami in ec2_data["ami_details"]:
                for ip in ec2_data["ip_details"]:
                    if platform[0] == ami[0] == ip[0]:
                        instance_id = ami[0]
                        arn = [f"arn:aws:ec2:{ec2_data['region']}:{ami[1]}:instance/{instance_id}"]

                        cs = cs_lookup.get(instance_id)
                        cs_agent_version = cs.get("agent_version", "NULL") if cs else "NULL"

                        enriched_data.append(
                            arn + ami + platform[1:] + ip[1:] +
                            [cs_agent_version]
                        )

        return enriched_data
