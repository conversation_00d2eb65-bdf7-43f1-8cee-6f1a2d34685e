# Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
"""
Lambda function to retrieve AWS resource and EC2 details, enrich with CrowdStrike and export to CSV.
To prevent the lambda from timeing out, this lambda runs for a single given region.
"""

import os
from concurrent.futures import ThreadPoolExecutor
import csv
import io
import logging
from datetime import datetime
import boto3

from crowdstrike_helper import CrowdStrikeClient

# Set up logger
logger = logging.getLogger("crowdstrike")
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)


def list_root_accounts():
    '''List all root aws accounts under the parent organisation'''
    root_id = "r-6kjv"
    client = boto3.client("organizations")
    paginator = client.get_paginator("list_accounts_for_parent")
    accounts = []
    for pag in paginator.paginate(ParentId=root_id):
        accounts += pag["Accounts"]
    new_accounts = [account["Id"] for account in accounts]
    return new_accounts

def list_all_accounts():
    '''List all aws accounts in under the root account'''
    accounts = []
    org = boto3.client("organizations")
    list_accounts_paginator = org.get_paginator("list_accounts")
    for page in list_accounts_paginator.paginate():
        accounts += page["Accounts"]
    root_accounts = list_root_accounts()

    # filter by active accounts and non root
    accounts = [
        account
        for account in accounts
        if account["Status"] == "ACTIVE" and account["Id"] not in root_accounts
    ]
    return accounts

def get_cross_account_role() -> str:
    '''Get the cross account role to be assumed in the target account'''
    parameter_store_client: boto3.SSM.Client = boto3.client(
        "ssm", region_name="eu-west-1"
    )
    adf_role_name = parameter_store_client.get_parameter(
        Name="cross_account_access_role"
    )
    return adf_role_name["Parameter"]["Value"]

def assume_cross_account_role(account_id) -> boto3.Session:
    '''Assume the cross account role in the target account'''
    role_session_name = "cross_account_role"
    role_arn = f"arn:aws:iam::{account_id}:role/{get_cross_account_role()}"
    logger.debug(role_arn)
    client: boto3.STS.Client = boto3.client("sts")
    sts_response = client.assume_role(
        RoleArn=role_arn, RoleSessionName=role_session_name
    )
    return boto3.Session(
        aws_access_key_id=sts_response["Credentials"]["AccessKeyId"],
        aws_secret_access_key=sts_response["Credentials"]["SecretAccessKey"],
        aws_session_token=sts_response["Credentials"]["SessionToken"],
    )

def get_all_resources(session, region_name):
    '''Get all resources for a specific region in an account'''
    client = session.client("resourcegroupstaggingapi", region_name=region_name)
    paginator = client.get_paginator("get_resources")
    page_iterator = paginator.paginate()
    all_resources = []
    for page in page_iterator:
        all_resources.extend(page["ResourceTagMappingList"])
    return all_resources


def generate_report_name(account_id, region_name):
    '''Generate resources csv file name'''
    current_time = datetime.now().strftime("%Y%m%d%H%M%S")
    report_name = f"aws_resource_csvs/resources_{account_id}_{region_name}_{current_time}.csv"
    return report_name

def generate_ec2_platform_details_report_name(account_id, region_name):
    '''Generate ec2 details csv file name'''
    current_time = datetime.now().strftime("%Y%m%d%H%M%S")
    ec2_report_name = f"ec2_detail_csvs/ec2_platform_details_{account_id}_{region_name}_{current_time}.csv"
    return ec2_report_name

# parse_arn function to format
def parse_arn(arn, account_id):
    '''Split the ARN into more human friendly properties'''
    arn_components = arn.split(":")
    service = arn_components[2]
    region_val = arn_components[3]
    aws_account = account_id
    resource_part = ":".join(arn_components[5:])
    if "/" in resource_part:
        resource_segments = resource_part.split("/")
        resource_type = resource_segments[0]
        identifier = "/".join(resource_segments[1:])
    elif ":" in resource_part:
        resource_segments = resource_part.split(":")
        resource_type = resource_segments[0]
        identifier = ":".join(resource_segments[1:])
    else:
        resource_type = ""
        identifier = resource_part
    combined_resource_type = f"{service}:{resource_type}" if resource_type else service
    return [identifier, arn, combined_resource_type, region_val, aws_account]

def prepare_csv_data(resources, account_id):
    '''Converte the resources data to csv friendly data'''
    # Define the resource types to exclude
    excluded_resource_types = ["secretsmanager:secret", "kms:key"]

    csv_data = [["Identifier", "ARN", "Resource_type", "Region", "AWS_Account"]]
    unique_tags = set()

    # Collect unique tags across all resources, excluding certain types
    for resource in resources:
        arn = resource["ResourceARN"]
        _, _, resource_type, _, _ = parse_arn(
            arn, account_id
        )  # Extract resource_type using parse_arn
        if resource_type in excluded_resource_types:
            continue  # Skip resources of types we want to exclude
        tags = resource.get("Tags", [])
        for tag in tags:
            unique_tags.add(f"Tags:{tag['Key']}")  # Add the prefix to the tag key

    # Sort and add unique tags to the CSV header
    unique_tags = sorted(unique_tags)
    csv_header = csv_data[0] + unique_tags
    csv_data[0] = csv_header

    # Process each resource and append row data, excluding specific types
    for resource in resources:
        arn = resource["ResourceARN"]
        row = parse_arn(arn, account_id)
        _, _, resource_type, _, _ = (
            row  # Re-extract resource_type to check for exclusion again
        )
        if resource_type in excluded_resource_types:
            continue  # Skip resources of types we want to exclude

        tags = {
            f"Tags:{tag['Key']}": tag["Value"] for tag in resource.get("Tags", [])
        }  # Add the prefix to the tag key

        # Append tag values or placeholders for non-excluded resources
        for tag in unique_tags:
            row.append(tags.get(tag, "(not tagged)"))

        csv_data.append(row)

    return csv_data

def get_ec2_platform_details_csv(session, region_name: str, account_id: str) -> list[list[str]]:
    """Get EC2 details for the region and enrich with CrowdStrike data."""
    ec2_client = session.client('ec2', region_name=region_name)
    ssm_client = session.client('ssm', region_name=region_name)

    # Fetch EC2 instance metadata
    # We can add pagination here but the by default we get 1000 results so for now this is not needed
    # When we want to add it refer to this: https://boto3.amazonaws.com/v1/documentation/api/latest/guide/paginators.html
    ec2_list = ec2_client.describe_instances(Filters=[{
        'Name': 'instance-state-name',
        'Values': ['running', 'stopped']
    }])

    ami_details = []
    ip_details = []

    for reservation in ec2_list['Reservations']:
        for instance in reservation['Instances']:
            ami_details.append([
                instance['InstanceId'],
                reservation['OwnerId'],
                instance['ImageId'],
                instance['State']['Name']
            ])
            ip_details.append([
                instance['InstanceId'],
                instance.get("PublicIpAddress", "NULL"),
                instance.get("PrivateIpAddress", "NULL"),
                instance.get("SubnetId", "NULL")
            ])

    # Fetch platform details from SSM
    # We can add pagination here but the by default we get 1000 results so for now this is not needed
    # When we want to add it refer to this: https://boto3.amazonaws.com/v1/documentation/api/latest/guide/paginators.html
    instance_info = ssm_client.get_inventory(Filters=[{
        'Key': 'AWS:InstanceInformation.InstanceStatus',
        'Values': ['Active', 'Stopped'],
        'Type': 'Equal'
    }])

    platform_details = []
    for entity in instance_info['Entities']:
        info = entity['Data']['AWS:InstanceInformation']['Content'][0]
        platform_details.append([
            entity['Id'],
            info.get("AgentVersion", "NULL"),
            info.get("PlatformType", "NULL"),
            info.get("PlatformName", "NULL"),
            info.get("PlatformVersion", "NULL"),
            region_name
        ])

    # Enrich EC2 data
    ec2_data = {
        "region": region_name,
        "ami_details": ami_details,
        "ip_details": ip_details,
        "platform_details": platform_details
    }

    csv_header = [
        "ARN", "InstanceId", 'AccountId', 'AMI', 'Status',
        "SSM_AgentVersion", "PlatformType", "PlatformName", "PlatformVersion", 'Region',
        "PublicIpAddress", "PrivateIpAddress", "SubnetId",
        "CS_AgentVersion"
    ]

    cs_client = CrowdStrikeClient(logger)
    enriched_data = cs_client.enrich_ec2_data(ec2_data, account_id)

    csv_data = [csv_header] + enriched_data

    return csv_data

def upload_to_s3(report_name, csv_data):
    '''Upload the csv data to csv file in the s3 bucket'''
    s3_client = boto3.client("s3")
    csvio = io.StringIO()
    writer = csv.writer(csvio)
    writer.writerows(csv_data)

    # Name of bucket where all reports will be uploaded to.
    # Currently hosted in the logs account and to be reviewed
    bucket_name = "test-cloud-maturity-dashboard-logs"
    s3_client.put_object(
        Body=csvio.getvalue().encode("utf-8"), Bucket=bucket_name, Key=report_name
    )
    logger.debug("Uploaded %s to %s", report_name, bucket_name)

def process_account(account, region):
    '''Go through all accounts for given region'''
    account_id = account["Id"]

    regions = [region]

    for region_name in regions:
        session = boto3
        if (
            account_id != "************"
        ):  # Exclude the Master account when running these reports
            session = assume_cross_account_role(account_id)

        # Gather all AWS resources
        resources = get_all_resources(session, region_name)

        # Check if the CSV has more than just the header
        if resources and len(resources) > 1:
            csv_data = prepare_csv_data(resources, account_id)
            report_name = generate_report_name(account_id, region_name)
            upload_to_s3(report_name, csv_data)
        else:
            logger.warning("No resources found in region '%s' for account '%s'",
                           region_name, account_id)

        # Gather all EC2 platform details
        ec2_csv_data = get_ec2_platform_details_csv(session, region_name, account_id)

        # Check if the CSV has more than just the header
        if ec2_csv_data and len(ec2_csv_data) > 1:
            ec2_report_name = generate_ec2_platform_details_report_name(account_id, region_name)
            upload_to_s3(ec2_report_name, ec2_csv_data)
        else:
            logger.info("Only header or no EC2 data found in '%s' for account '%s', skipping upload.",
                        region_name, account_id)

def lambda_handler(_event, _context):
    '''Lambda handler function to process all accounts and regions'''
    region = os.environ.get("REGION_NAME")

    if region is None:
        raise ValueError("Missing required environment variable: REGION_NAME")

    accounts = list_all_accounts()

    with ThreadPoolExecutor(
        20
    ) as executor:  # Increased the parallel threads from 10 to 20 in order to speed up
                    # the processing time and stay within 15 min window
        for account in accounts:
            executor.submit(process_account, account, region)

    return {"statusCode": 200, "body": "Lambda function executed successfully!"}
